# Studio Sessions Schema Plan

## Overview
Create a comprehensive Sanity schema for studio sessions with different service types, pricing tiers, and booking options based on the provided pricing structure.

## Data Analysis
From the provided information:
- **Photoshoot (location)**: 2-4 hours (GHC 1600), 4-6 hours (GHC 2500), Base rate (GHC 1200)
- **Studio walk-in (studio artist)**: GHC 400
- **Studio walk-in (Cherry)**: GHC 750
- **Home service (within Accra)**: GHC 1200-1500, 2-3 hours (extra hour GHC 100)

## Schema Structure

### 1. Main Schema: `studioSession`
**Purpose**: Main document type for studio session services

**Fields**:
- `title` (string, required) - Service name
- `slug` (slug, required) - URL-friendly identifier
- `serviceType` (string, required) - Type of service (photoshoot, studio-walkin, home-service)
- `description` (text) - Detailed service description
- `shortDescription` (string) - Brief summary for cards/previews
- `pricing` (object) - Pricing structure (see pricing schema below)
- `duration` (object) - Duration options and details
- `location` (object) - Location-specific information
- `features` (array of strings) - What's included in the service
- `requirements` (text) - Client requirements or preparations
- `availability` (object) - Booking availability settings
- `images` (array of images) - Service gallery images
- `isActive` (boolean) - Whether service is currently offered
- `order` (number) - Display order
- `seo` (object) - SEO metadata

### 2. Pricing Object Structure
**Nested within studioSession**:
- `basePrice` (number) - Starting/base price
- `currency` (string) - Currency code (GHC)
- `pricingTiers` (array) - Different pricing options
  - `name` (string) - Tier name (e.g., "2-4 hours", "4-6 hours")
  - `price` (number) - Tier price
  - `duration` (object) - Min/max hours
  - `description` (string) - What's included
- `extraHourRate` (number) - Additional hour pricing (for applicable services)
- `priceRange` (object) - Min/max pricing for range-based services

### 3. Duration Object Structure
**Nested within studioSession**:
- `minHours` (number) - Minimum session duration
- `maxHours` (number) - Maximum session duration
- `defaultHours` (number) - Standard session length
- `isFlexible` (boolean) - Whether duration can be customized
- `extraHourAvailable` (boolean) - Whether extra hours can be added

### 4. Location Object Structure
**Nested within studioSession**:
- `type` (string) - location, studio, home, mobile
- `address` (string) - Specific address if applicable
- `area` (string) - General area (e.g., "within Accra")
- `travelFee` (number) - Additional travel costs if applicable
- `coordinates` (geopoint) - Map coordinates if needed

### 5. Supporting Schema: `serviceCategory`
**Purpose**: Categorize different types of studio services

**Fields**:
- `title` (string, required)
- `slug` (slug, required)
- `description` (text)
- `icon` (image) - Category icon
- `color` (color) - Brand color for category
- `order` (number)

## Predefined Service Types
Based on the pricing data, we'll create these initial services:

1. **Location Photoshoot**
   - Service Type: `photoshoot`
   - Pricing Tiers: 2-4 hours (GHC 1600), 4-6 hours (GHC 2500)
   - Base Price: GHC 1200

2. **Studio Walk-in (Studio Artist)**
   - Service Type: `studio-walkin`
   - Fixed Price: GHC 400
   - Artist: Studio Artist

3. **Studio Walk-in (Cherry)**
   - Service Type: `studio-walkin`
   - Fixed Price: GHC 750
   - Artist: Cherry (Premium)

4. **Home Service (Accra)**
   - Service Type: `home-service`
   - Price Range: GHC 1200-1500
   - Duration: 2-3 hours
   - Extra Hour: GHC 100

## Query Functions Plan

### 1. Basic Queries
- `getAllStudioSessions()` - Get all active studio sessions
- `getStudioSessionBySlug(slug)` - Get specific session by slug
- `getStudioSessionsByType(serviceType)` - Filter by service type
- `getFeaturedStudioSessions()` - Get highlighted services

### 2. Pricing Queries
- `getStudioSessionPricing(slug)` - Get pricing details for a service
- `getServicesByPriceRange(min, max)` - Filter by price range
- `getCheapestService()` - Get most affordable option
- `getPremiumServices()` - Get high-end services

### 3. Booking Queries
- `getAvailableServices()` - Get currently bookable services
- `getServicesByDuration(hours)` - Filter by session length
- `getHomeServices()` - Get mobile/home services only
- `getStudioServices()` - Get studio-based services only

### 4. Admin Queries
- `getAllStudioSessionsAdmin()` - Get all sessions including inactive
- `getStudioSessionStats()` - Get analytics data
- `getPopularServices()` - Get most booked services

## Utility Functions Plan

### 1. Price Calculation
- `calculateSessionPrice(serviceSlug, duration, extras)` - Calculate total price
- `formatPrice(amount, currency)` - Format price display
- `getPriceRange(serviceSlug)` - Get min/max pricing
- `calculateExtraHours(baseHours, totalHours, hourlyRate)` - Extra hour calculation

### 2. Availability Functions
- `isServiceAvailable(serviceSlug)` - Check if service is bookable
- `getServiceDuration(serviceSlug)` - Get duration options
- `validateBookingRequest(serviceSlug, duration, location)` - Validate booking

### 3. Display Functions
- `getServiceSummary(serviceSlug)` - Get service overview
- `formatDuration(hours)` - Format duration display
- `getServiceFeatures(serviceSlug)` - Get included features list

## File Structure
```
schemas/
├── documents/
│   ├── studioSession.js
│   └── serviceCategory.js
├── objects/
│   ├── pricing.js
│   ├── duration.js
│   └── location.js
└── index.js

lib/
├── sanity/
│   ├── queries/
│   │   ├── studioSessions.js
│   │   └── index.js
│   └── utils/
│       ├── pricing.js
│       ├── availability.js
│       └── formatting.js
```

## Next Steps
1. Review and approve this plan
2. Create the Sanity schema files
3. Implement the query functions
4. Create utility functions
5. Add TypeScript types
6. Test with sample data
7. Create admin interface components

## Questions for Review
1. Should we add booking/appointment functionality to the schema?
2. Do we need artist profiles as separate documents?
3. Should we include client testimonials/reviews in the schema?
4. Do we need seasonal pricing or promotional pricing fields?
5. Should we track booking history/analytics in the schema?

Please review this plan and let me know if you'd like any modifications before I proceed with the implementation.
