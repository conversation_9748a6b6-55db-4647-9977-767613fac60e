import { Metadata } from "next";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "Studio Sessions",
  description:
    "View Our Studio Sessions, Explore Our Beauty Creations.",
  openGraph: {
    title: "Studio Sessions | Black Cherry",
    description:
      "View Our Studio Sessions, Explore Our Beauty Creations.",
    url: "https://www.blackcherrygh.com/studio-sessions/",
    siteName: "Studio Sessions | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Studio Sessions | Black Cherry",
    description:
      "View Our Studio Sessions, Explore Our Beauty Creations.",
    site: "https://www.blackcherrygh.com/studio-sessions/",
  },
};

export default function Page() {
  return (
    <main className="mx-auto w-full max-w-7xl">
      <section className="">this is the studio session page</section>
    </main>
  );
}
