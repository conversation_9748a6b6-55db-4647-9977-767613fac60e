"use client";

import { buttonVariants } from "@/components/ui/button";
import { FONT_PLAYFAIR_DISPLAY, aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import aboutImg from "@/public/images/me/me001.jpeg";
import aboutImg2 from "@/public/images/me/me005.jpeg";
import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const container = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export function FounderProfile() {
  const [showSecondImage, setShowSecondImage] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!contentRef.current || !sectionRef.current) return;

      const sectionRect = sectionRef.current.getBoundingClientRect();
      const contentRect = contentRef.current.getBoundingClientRect();

      // Check if the section is in view
      if (sectionRect.top <= window.innerHeight && sectionRect.bottom >= 0) {
        // Calculate scroll progress within the content area
        const scrollProgress =
          Math.abs(contentRect.top) / (contentRect.height - window.innerHeight);

        // Trigger image transition when scrolled to about 30% of the content (around 3rd paragraph)
        setShowSecondImage(scrollProgress > 0.3);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Check initial state

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <section ref={sectionRef} className="w-full py-16">
      <div className="mx-auto max-w-5xl px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={container}
          className="grid items-start gap-12 md:grid-cols-2"
        >
          {/* Left side - Profile Image and Quick Stats */}
          <motion.div variants={fadeInUp} className="sticky top-24 space-y-6">
            <div className="relative overflow-hidden rounded-2xl">
              <div className="aspect-[3/4] w-full">
                {/* Replace with actual profile image path */}
                <div className="absolute inset-0 bg-black/10" />

                {/* First Image */}
                <Image
                  src={aboutImg}
                  alt="Linda Mensah - Founder of Blackcherry"
                  width={600}
                  height={800}
                  className={`h-full w-full object-cover transition-all duration-1000 ease-in-out ${
                    showSecondImage ? "opacity-0 blur-sm" : "blur-0 opacity-100"
                  }`}
                />

                {/* Second Image */}
                <Image
                  src={aboutImg2}
                  alt="Linda Mensah - Founder of Blackcherry"
                  width={600}
                  height={800}
                  className={`absolute inset-0 h-full w-full object-cover transition-all duration-1000 ease-in-out ${
                    showSecondImage ? "blur-0 opacity-100" : "opacity-0 blur-sm"
                  }`}
                />
              </div>
            </div>
          </motion.div>

          {/* Right side - Content */}
          <motion.div
            ref={contentRef}
            variants={fadeInUp}
            className="space-y-8 p-4"
          >
            <div>
              <h2
                className={cn(
                  FONT_PLAYFAIR_DISPLAY.className,
                  "mb-4 text-3xl font-medium",
                )}
              >
                About
              </h2>
              <h3
                className={cn(aldineBT.className, "mb-6 text-xl text-gray-600")}
              >
                Founder & Lead Beauty Consultant
              </h3>
              <div className="space-y-4 text-gray-700">
                {/* <p>
                  Licensed Aesthetician specializing in Medical Aesthetics,
                  Semi-Permanent Makeup (SPMU), and Cosmetic Science. Expert
                  trainer and facilitator with proven project management skills
                  and multilingual capabilities (English, Twi, Fante, Ga).
                </p>
                <p>
                  Linda Mensah is the visionary founder of Blackcherry, a
                  renowned beauty and wellness brand specializing in natural and
                  glamorous makeup & skincare services for high melanin ladies.
                </p>
                <p>
                  With a passion for natural beauty and a commitment to
                  chemical-free products, Linda has built Blackcherry into a
                  comprehensive beauty destination that encompasses professional
                  services, education, and wellness.
                </p> */}
                <p>
                  Linda Mensah is the visionary founder of Blackcherry, a
                  celebrated beauty and wellness brand dedicated to offering
                  natural and glamorous makeup and skincare services tailored
                  for women with melanin-rich skin.
                </p>
                <p>
                  A Licensed Medical Aesthetician with specializations in Dermal
                  Therapy, Semi-Permanent Makeup (SPMU), and Cosmetic Science,
                  Ms. Mensah is also a highly sought-after trainer and
                  facilitator. She has empowered over 1,000 individuals through
                  professional training in Cosmetology and Makeup Artistry.{" "}
                </p>
                <p>
                  Under her leadership, the Blackcherry team has served as the
                  official makeup providers for major productions such as
                  Ghana’s Most Beautiful, Miss Malaika Ghana, Miss Universe
                  Ghana, and several other high-profile commercial projects.
                </p>
                <p>
                  A trusted consultant and brand ambassador, Ms. Mensah has
                  collaborated with global beauty giants like MAC Cosmetics and
                  Max Beauty, spearheading impactful initiatives to inspire and
                  mentor the next generation of beauty professionals. Today,
                  Blackcherry stands as one of Ghana’s most in-demand beauty
                  service providers and training institutes, especially
                  recognized for excellence in corporate beauty services.{" "}
                </p>
                <p>
                  Driven by a passion for natural beauty and a strong commitment
                  to chemical-free products, Ms. Mensah has transformed
                  Blackcherry into a holistic beauty hub offering premium
                  services, education, and wellness solutions. Her dedication
                  has also birthed two remarkable brands: • Sey Naturelle
                  Cosmetics: a 100% natural skincare line designed to address
                  skin concerns for women of color. • Covered Cosmetics: an
                  innovative makeup brand focused on creating healthy,
                  inclusive, and high-performance beauty products. We invite you
                  to explore our world of beauty—and we look forward to sharing
                  a blissful, transformative experience with you!
                </p>
              </div>
            </div>

            {/* <div className="space-y-4">
              <h4 className={cn(aldineBT.className, "text-xl font-medium")}>
                Our Divisions
              </h4>
              <ul className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">BC 360 Aesthetic Clinic</h5>
                  <p className="text-sm text-gray-600">
                    Specialized skincare services and natural remedies
                  </p>
                </li>
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">Sey Naturelle</h5>
                  <p className="text-sm text-gray-600">
                    Chemical-free cosmetic line with Ayurvedic herbs
                  </p>
                </li>
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">Beauty Academy</h5>
                  <p className="text-sm text-gray-600">
                    Professional beauty education and training
                  </p>
                </li>
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">Bridal Services</h5>
                  <p className="text-sm text-gray-600">
                    Specialized makeup and beauty services
                  </p>
                </li>
              </ul>
            </div> */}

            <div className="flex flex-wrap gap-4">
              {/* <Link
                href="/contact"
                className={cn(
                  buttonVariants({
                    variant: "black",
                    size: "withIconRight",
                  }),
                  "group space-x-2",
                )}
              >
                <span>Book a Consultation</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-white" />
                </div>
              </Link> */}
              <Link
                href="/bridal-bookings-ratecard"
                className={cn(
                  buttonVariants({
                    variant: "outline",
                    size: "withIconRight",
                  }),
                  "group space-x-2",
                )}
              >
                <span>View Rate Card</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                </div>
              </Link>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
